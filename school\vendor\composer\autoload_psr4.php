<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'WpOrg\\Requests\\' => array($vendorDir . '/rmccue/requests/src'),
    'Tests\\Support\\' => array($baseDir . '/tests/_support'),
    'Svg\\' => array($vendorDir . '/dompdf/php-svg-lib/src/Svg'),
    'Sabberworm\\CSS\\' => array($vendorDir . '/sabberworm/php-css-parser/src'),
    'Razorpay\\Tests\\' => array($vendorDir . '/razorpay/razorpay/tests'),
    'Razorpay\\Api\\' => array($vendorDir . '/razorpay/razorpay/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'Masterminds\\' => array($vendorDir . '/masterminds/html5/src'),
    'Laminas\\Escaper\\' => array($vendorDir . '/laminas/laminas-escaper/src'),
    'FontLib\\' => array($vendorDir . '/dompdf/php-font-lib/src/FontLib'),
    'Faker\\' => array($vendorDir . '/fakerphp/faker/src/Faker'),
    'Dompdf\\' => array($vendorDir . '/dompdf/dompdf/src'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Config\\' => array($baseDir . '/app/Config'),
    'CodeIgniter\\' => array($vendorDir . '/codeigniter4/framework/system'),
    'App\\' => array($baseDir . '/app'),
);
