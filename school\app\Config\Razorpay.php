<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Razorpay extends BaseConfig
{
    /**
     * Razorpay Test Mode Configuration
     * Set to false for production
     */
    public bool $testMode = true;

    /**
     * Test API Keys (Use these for development)
     */
    public string $testKeyId = 'rzp_test_1DP5mmOlF5G5ag';
    public string $testKeySecret = 'thisissecretkey';

    /**
     * Production API Keys (Replace with your actual keys)
     */
    public string $liveKeyId = 'rzp_live_your_key_id';
    public string $liveKeySecret = 'your_live_secret_key';

    /**
     * Webhook Secret (for verifying webhook signatures)
     */
    public string $webhookSecret = 'your_webhook_secret';

    /**
     * Currency
     */
    public string $currency = 'INR';

    /**
     * Payment timeout in seconds
     */
    public int $paymentTimeout = 900; // 15 minutes

    /**
     * Get current API keys based on mode
     */
    public function getKeyId(): string
    {
        return $this->testMode ? $this->testKeyId : $this->liveKeyId;
    }

    public function getKeySecret(): string
    {
        return $this->testMode ? $this->testKeySecret : $this->liveKeySecret;
    }

    /**
     * Get payment options
     */
    public function getPaymentOptions(): array
    {
        return [
            'currency' => $this->currency,
            'timeout' => $this->paymentTimeout,
            'theme' => [
                'color' => '#4F46E5' // Indigo color matching the app theme
            ],
            'modal' => [
                'ondismiss' => 'function(){console.log("Payment cancelled")}'
            ],
            'prefill' => [
                'method' => 'card'
            ]
        ];
    }

    /**
     * Supported payment methods
     */
    public function getSupportedMethods(): array
    {
        return [
            'card',
            'netbanking',
            'wallet',
            'upi'
        ];
    }
}
